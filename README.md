# PandasAI with Qwen-max 数据分析应用

这是一个基于 PandasAI 和阿里云百炼 Qwen-max 模型的数据分析 Web 应用程序。用户可以通过简洁的前端界面上传数据文件（CSV 或 Excel），输入自然语言问题，后端将利用大模型进行数据分析，并将结果（文本或图表）返回到前端展示。

## 功能特性

- 📊 支持 CSV 和 Excel 文件上传
- 🤖 集成阿里云百炼 Qwen-max 大模型
- 📈 自动生成数据可视化图表
- 💬 自然语言查询数据
- 🎨 现代化的 Web 界面

## 项目结构

```
PandaAI/
├── .env                    # 环境变量配置文件
├── app.py                  # Flask 后端应用
├── requirements.txt        # Python 依赖包
├── # PandasAI 标准化集成方案

## 🎯 项目概述

本项目实现了**基于API中间层的PandasAI集成与标准化方案**，解决了PandasAI动态输出给前端开发带来的挑战。通过引入响应标准化模块，将PandasAI的不可预测输出转换为统一、可靠的API响应格式。

## ✨ 核心特性

- **🔄 响应标准化**: 将PandasAI的动态输出转换为统一的JSON格式
- **🎨 多种结果类型**: 支持表格、文本、图表、JSON等多种数据展示
- **🛡️ 错误处理**: 标准化的错误响应格式
- **📱 前后端解耦**: 前端无需关心PandasAI内部实现
- **🔧 双端点支持**: 提供文件上传和JSON API两种接口
- **📊 智能数据处理**: 自动处理大数据集截断和图表转换

## 🏗️ 架构设计

```
┌───────────────┐      ┌─────────────────────────┐      ┌────────────────────┐      ┌──────────────┐
│  前端应用     │  ──> │   后端 API 服务器        │  ──> │  PandasAI 服务层   │  ──> │  数据源       │
│ (React/Vue/etc) │ <── │  (Python: FastAPI/Flask)│ <── │  (核心逻辑)        │ <── │ (数据库/文件) │
└───────────────┘      └─────────────────────────┘      └────────────────────┘      └──────────────┘
                         │
                         └─ [关键：响应标准化模块]
```

## 📋 标准化响应格式

所有API响应都遵循统一格式：

```json
{
  "status": "success" | "error",
  "resultType": "table" | "text" | "plot" | "raw_json" | "error",
  "data": { ... },
  "errorDetails": null | {
    "code": "错误代码",
    "message": "错误描述"
  }
}
```

### 响应类型说明

| 类型 | 说明 | 数据格式 |
|------|------|----------|
| `table` | 表格数据 | `{columns: [], data: [], metadata: {}}` |
| `text` | 文本答案 | `{content: "文本内容"}` |
| `plot` | 图表 | `{image: "base64数据", caption: "标题"}` |
| `raw_json` | JSON数据 | 任意JSON对象 |
| `error` | 错误 | `null` (错误信息在errorDetails中) |

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd PandaAI

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

创建 `.env` 文件：

```env
DASHSCOPE_API_KEY=your_api_key_here
OPENAI_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MODEL=qwen-plus
LLM_TEMPERATURE=0.1
LLM_MAX_TOKENS=4000
```

### 3. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5001` 启动

### 4. 访问界面

- **主界面**: http://localhost:5001/
- **API演示**: http://localhost:5001/demo

## 🔌 API使用

### 标准化API端点

**POST** `/api/query`

```javascript
const response = await fetch('/api/query', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    query: "总销售额是多少？",
    data: [
      {"产品": "A", "销售额": 1000},
      {"产品": "B", "销售额": 1500}
    ]
  })
});

const result = await response.json();

if (result.status === 'success') {
  switch (result.resultType) {
    case 'table':
      renderTable(result.data);
      break;
    case 'text':
      showText(result.data.content);
      break;
    case 'plot':
      showImage(result.data.image);
      break;
  }
}
```

### 文件上传端点

**POST** `/query`

```javascript
const formData = new FormData();
formData.append('file', fileInput.files[0]);
formData.append('query', '总销售额是多少？');

const response = await fetch('/query', {
  method: 'POST',
  body: formData
});
```

## 🧪 测试

运行测试脚本验证功能：

```bash
# 启动应用
python app.py

# 在另一个终端运行测试
python test_standardized_api.py
```

## 📁 项目结构

```
PandaAI/
├── app.py                      # 主应用文件
├── requirements.txt            # 依赖列表
├── .env                       # 环境变量配置
├── templates/
│   ├── index.html             # 主界面
│   └── api_demo.html          # API演示页面
├── test_standardized_api.py   # API测试脚本
├── API_USAGE_GUIDE.md         # API使用指南
└── README.md                  # 项目说明
```

## 🎨 核心组件

### ResponseStandardizer 类

负责将PandasAI的动态输出标准化：

```python
class ResponseStandardizer:
    @staticmethod
    def standardize_response(raw_result, error=None):
        """将PandasAI结果转换为标准格式"""
        # 处理不同类型的结果
        if isinstance(raw_result, pd.DataFrame):
            return _handle_dataframe(raw_result)
        elif isinstance(raw_result, str):
            return _handle_string(raw_result)
        # ... 其他类型处理
```

### QwenLLM 类

自定义LLM实现，支持阿里云百炼API：

```python
class QwenLLM(LLM):
    def generate_code(self, query, context):
        """生成高质量的pandas代码"""
        # 专门优化的代码生成逻辑
```

## 🔧 配置选项

| 环境变量 | 说明 | 默认值 |
|----------|------|--------|
| `DASHSCOPE_API_KEY` | 阿里云API密钥 | 必需 |
| `OPENAI_API_BASE` | API基础URL | 必需 |
| `LLM_MODEL` | 模型名称 | qwen-plus |
| `LLM_TEMPERATURE` | 温度参数 | 0.1 |
| `LLM_MAX_TOKENS` | 最大令牌数 | 4000 |

## 🎯 优势特点

1. **前后端彻底解耦**: 前端开发者无需了解PandasAI内部机制
2. **可预测的接口**: 统一的响应格式，降低前端开发复杂度
3. **高可扩展性**: 易于添加新的结果类型和处理逻辑
4. **安全可靠**: 完善的错误处理和资源清理机制
5. **向后兼容**: 保留原有接口，平滑迁移

## 📚 文档

- [API使用指南](API_USAGE_GUIDE.md) - 详细的API使用说明
- [测试脚本](test_standardized_api.py) - 完整的功能测试

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License              # 项目说明文档
└── templates/
    └── index.html         # 前端界面
```

## 安装和配置

### 1. 配置 API Key

编辑 `.env` 文件，将您的阿里云百炼 API Key 替换到相应位置：

```env
DASHSCOPE_API_KEY="您的真实API_KEY"
```

### 2. 安装依赖

确保您在项目根目录下，然后运行：

```bash
pip install -r requirements.txt
```

### 3. 启动应用

```bash
python app.py
```

### 4. 访问应用

打开浏览器访问：`http://127.0.0.1:5001`

## 使用方法

1. **上传数据文件**：选择您的 CSV 或 Excel 文件
2. **输入问题**：用自然语言描述您想要分析的问题
3. **提交分析**：点击"提交分析"按钮
4. **查看结果**：在右侧面板查看分析结果或生成的图表

## 示例问题

- "总销售额是多少？"
- "按城市绘制销售额的条形图"
- "哪个产品的销量最高？"
- "显示每月的销售趋势"
- "计算平均客单价"

## 技术栈

- **后端**：Flask, PandasAI, Pandas
- **前端**：HTML, CSS, JavaScript
- **AI模型**：阿里云百炼 Qwen-max
- **数据处理**：Pandas, OpenPyXL
- **可视化**：Matplotlib, Seaborn

## 注意事项

- 确保您的阿里云百炼 API Key 有效且有足够的调用额度
- 上传的文件大小建议不超过 10MB
- 支持的文件格式：CSV (.csv) 和 Excel (.xlsx)
- 应用运行在开发模式，生产环境请进行相应配置

## 故障排除

如果遇到问题，请检查：

1. API Key 是否正确配置
2. 所有依赖包是否正确安装
3. 网络连接是否正常
4. 上传的文件格式是否支持

### 已解决的问题

✅ **"Type has not been implemented" 错误**：已通过自定义 LLM 类解决，使用阿里云百炼的 OpenAI 兼容接口

✅ **模型名称验证错误**：PandasAI 的 OpenAI 类对模型名称有严格验证，已通过自定义实现绕过

✅ **API 配置问题**：已正确配置阿里云百炼的 OpenAI 兼容接口参数
