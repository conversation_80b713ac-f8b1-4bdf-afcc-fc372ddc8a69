#!/usr/bin/env python3
"""
测试配置脚本 - 验证 PandasAI 和阿里云百炼的配置是否正确
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# 加载环境变量
load_dotenv()

def test_config():
    """测试配置是否正确"""
    print("🔧 测试 PandasAI 配置...")
    
    # 检查环境变量
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-max")
    
    print(f"✅ API Key: {'已配置' if api_key else '❌ 未配置'}")
    print(f"✅ API Base: {api_base}")
    print(f"✅ Model: {model}")
    
    if not api_key or not api_base:
        print("❌ 配置不完整，请检查 .env 文件")
        return False
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            '产品': ['手机', '电脑', '平板'],
            '销量': [100, 50, 75],
            '价格': [3000, 8000, 2000]
        })
        
        print("📊 创建测试数据...")
        print(test_data)
        
        # 初始化 LLM
        llm = OpenAI(
            api_token=api_key,
            api_base=api_base,
            model=model,
            temperature=0.1,
            max_tokens=4000
        )
        
        print("🤖 初始化 LLM...")
        
        # 创建 SmartDataframe
        smart_df = SmartDataframe(test_data, config={"llm": llm})
        
        print("🧠 测试简单查询...")
        
        # 测试简单查询
        result = smart_df.chat("总销量是多少？")
        print(f"📈 查询结果: {result}")
        
        print("✅ 配置测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

if __name__ == "__main__":
    test_config()
