<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PandasAI 标准化API演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background-color: #fafafa;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #555;
        }
        .query-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 10px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result-container {
            margin-top: 15px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
            min-height: 100px;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 4px;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        .result-table th, .result-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .result-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .metadata {
            background-color: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-top: 10px;
        }
        .data-preview {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .data-preview h4 {
            margin-top: 0;
            color: #495057;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 PandasAI 标准化API演示</h1>
        
        <div class="data-preview">
            <h4>📊 演示数据集</h4>
            <p>本演示使用以下示例数据：</p>
            <pre id="sample-data"></pre>
        </div>

        <div class="demo-section">
            <h3>💬 自定义查询</h3>
            <input type="text" id="custom-query" class="query-input" 
                   placeholder="输入您的数据分析问题，例如：总销售额是多少？">
            <br>
            <button class="btn" onclick="runCustomQuery()">执行查询</button>
            <div id="custom-result" class="result-container"></div>
        </div>

        <div class="demo-section">
            <h3>🧪 预设查询示例</h3>
            <p>点击下面的按钮测试不同类型的查询：</p>
            
            <button class="btn" onclick="runPresetQuery('总销售额是多少？')">
                📊 总销售额
            </button>
            <button class="btn" onclick="runPresetQuery('按销售额从高到低排序显示所有产品')">
                📋 排序表格
            </button>
            <button class="btn" onclick="runPresetQuery('计算平均销售额')">
                🔢 平均值
            </button>
            <button class="btn" onclick="runPresetQuery('显示销售额大于1000的产品')">
                🔍 筛选数据
            </button>
            <button class="btn" onclick="runPresetQuery('画一个显示各产品销售额的柱状图')">
                📈 生成图表
            </button>
            
            <div id="preset-result" class="result-container"></div>
        </div>

        <div class="demo-section">
            <h3>🔧 API响应格式</h3>
            <p>查看最后一次API调用的原始响应：</p>
            <pre id="raw-response">暂无数据</pre>
        </div>
    </div>

    <script>
        // 示例数据
        const sampleData = [
            {"产品名称": "产品A", "销售额": 1000, "销量": 50, "价格": 20, "城市": "北京"},
            {"产品名称": "产品B", "销售额": 1500, "销量": 75, "价格": 20, "城市": "上海"},
            {"产品名称": "产品C", "销售额": 800, "销量": 40, "价格": 20, "城市": "广州"},
            {"产品名称": "产品D", "销售额": 2000, "销量": 100, "价格": 20, "城市": "深圳"},
            {"产品名称": "产品E", "销售额": 1200, "销量": 60, "价格": 20, "城市": "杭州"}
        ];

        // 显示示例数据
        document.getElementById('sample-data').textContent = JSON.stringify(sampleData, null, 2);

        // 执行自定义查询
        async function runCustomQuery() {
            const query = document.getElementById('custom-query').value.trim();
            if (!query) {
                alert('请输入查询问题');
                return;
            }
            await executeQuery(query, 'custom-result');
        }

        // 执行预设查询
        async function runPresetQuery(query) {
            await executeQuery(query, 'preset-result');
        }

        // 执行查询的核心函数
        async function executeQuery(query, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            const rawResponseElement = document.getElementById('raw-response');
            
            // 显示加载状态
            resultElement.innerHTML = '<div class="loading">🧠 正在分析中，请稍候...</div>';
            
            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        data: sampleData
                    })
                });

                const result = await response.json();
                
                // 显示原始响应
                rawResponseElement.textContent = JSON.stringify(result, null, 2);
                
                // 渲染结果
                if (result.status === 'success') {
                    renderResult(result, resultElement);
                } else {
                    renderError(result, resultElement);
                }

            } catch (error) {
                resultElement.innerHTML = `
                    <div class="error">
                        <strong>网络错误:</strong> ${error.message}
                    </div>
                `;
                rawResponseElement.textContent = `网络错误: ${error.message}`;
            }
        }

        // 渲染成功结果
        function renderResult(result, element) {
            const { resultType, data } = result;
            
            let html = `<div class="success"><strong>✅ 查询成功</strong> (类型: ${resultType})</div>`;
            
            switch (resultType) {
                case 'table':
                    html += renderTable(data);
                    break;
                case 'text':
                    html += `<p><strong>结果:</strong> ${data.content}</p>`;
                    break;
                case 'plot':
                    html += `
                        <p><strong>${data.caption}</strong></p>
                        <img src="${data.image}" alt="Generated Plot" style="max-width: 100%; border: 1px solid #ddd; border-radius: 4px;">
                    `;
                    break;
                case 'raw_json':
                    html += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    break;
                default:
                    html += `<p>未知的结果类型: ${resultType}</p>`;
            }
            
            element.innerHTML = html;
        }

        // 渲染表格
        function renderTable(data) {
            const { columns, data: rows, metadata } = data;
            
            let html = '<table class="result-table"><thead><tr>';
            columns.forEach(col => {
                html += `<th>${col}</th>`;
            });
            html += '</tr></thead><tbody>';
            
            rows.forEach(row => {
                html += '<tr>';
                row.forEach(cell => {
                    html += `<td>${cell !== null ? cell : ''}</td>`;
                });
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            
            if (metadata) {
                html += `
                    <div class="metadata">
                        <strong>数据信息:</strong> 
                        共 ${metadata.total_rows} 行，${metadata.columns_count} 列
                        ${metadata.truncated ? `（显示前 ${metadata.displayed_rows} 行）` : ''}
                    </div>
                `;
            }
            
            return html;
        }

        // 渲染错误
        function renderError(result, element) {
            const errorDetails = result.errorDetails || { code: 'Unknown', message: '未知错误' };
            element.innerHTML = `
                <div class="error">
                    <strong>❌ 查询失败</strong><br>
                    <strong>错误类型:</strong> ${errorDetails.code}<br>
                    <strong>错误信息:</strong> ${errorDetails.message}
                </div>
            `;
        }
    </script>
</body>
</html>
