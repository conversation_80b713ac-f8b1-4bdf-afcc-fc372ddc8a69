#!/usr/bin/env python3
"""
调试代码生成
"""

import os
import pandas as pd
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_direct_code_generation():
    """直接测试代码生成"""
    print("🔧 直接测试代码生成...")
    
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    if not api_key or not api_base:
        print("❌ 配置不完整")
        return
    
    # 构建专门用于代码生成的提示
    query = "总销售额是多少，请同时为我输出分析内容"
    context = "DataFrame with columns: 产品, 销量, 价格"
    
    prompt = f"""
Based on the following data analysis question, generate Python pandas code to answer it.

Question: {query}

Data context: {context}

Requirements:
1. Use pandas operations (df is the dataframe variable)
2. The final answer must be stored in a variable called 'result'
3. Generate ONLY executable Python code, no markdown formatting
4. Do not include ```python or ``` tags
5. Make sure the last line assigns the final answer to 'result'

Example:
# Calculate total sales
total_sales = df['销量'] * df['价格']
result = total_sales.sum()

Please generate the appropriate pandas code (code only, no explanations):
"""
    
    url = f"{api_base}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "You are a Python data analysis expert. Generate ONLY clean, executable pandas code. No markdown formatting, no explanations, just pure Python code. Always ensure the final result is stored in a variable called 'result'."
            },
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 4000
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"].strip()
            
            print(f"🤖 原始响应:")
            print(f"'{content}'")
            print()
            
            # 清理响应，移除任何 markdown 格式
            if "```python" in content:
                # 提取代码块中的内容
                import re
                code_match = re.search(r'```python\s*(.*?)\s*```', content, re.DOTALL)
                if code_match:
                    content = code_match.group(1).strip()
            elif "```" in content:
                # 移除任何代码块标记
                import re
                content = re.sub(r'```.*?\n', '', content)
                content = re.sub(r'```', '', content)
                content = content.strip()
            
            print(f"🧹 清理后的代码:")
            print(f"'{content}'")
            print()
            
            # 如果内容不像代码，生成一个基本的代码
            if not any(keyword in content for keyword in ['df', '=', 'result']):
                content = "result = df.sum().sum()  # 计算所有数值列的总和"
                print("⚠️ 内容不像代码，使用默认代码")
            
            # 确保代码的最后一行是 result =
            lines = content.split('\n')
            if lines and 'result =' not in lines[-1]:
                # 如果最后一行不是 result = ，尝试修复
                if len(lines) > 1:
                    # 多行代码，确保最后一行赋值给 result
                    last_line = lines[-1].strip()
                    if '=' in last_line and 'result' not in last_line:
                        # 最后一行是赋值，但不是给 result
                        var_name = last_line.split('=')[0].strip()
                        lines[-1] = f"result = {var_name}"
                    elif not last_line.startswith('result'):
                        # 最后一行不是赋值，添加 result =
                        lines.append(f"result = {last_line}")
                else:
                    # 单行代码
                    if content.strip() and not content.strip().startswith('result'):
                        content = f"result = {content.strip()}"
                content = '\n'.join(lines)
                print("⚠️ 修复了 result 变量赋值")
            
            print(f"✅ 最终代码:")
            print(f"'{content}'")
            
            # 测试代码执行
            test_data = pd.DataFrame({
                '产品': ['手机', '电脑', '平板'],
                '销量': [100, 50, 75],
                '价格': [3000, 8000, 2000]
            })
            
            try:
                df = test_data
                local_vars = {'df': df}
                exec(content, {}, local_vars)
                print(f"🎯 执行结果: {local_vars.get('result', 'No result')}")
            except Exception as e:
                print(f"❌ 代码执行错误: {e}")
            
        else:
            print("❌ API 返回格式错误")
            
    except Exception as e:
        print(f"❌ API 调用错误: {e}")

if __name__ == "__main__":
    test_direct_code_generation()
