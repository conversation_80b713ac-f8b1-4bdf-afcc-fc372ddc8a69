#!/usr/bin/env python3
"""
测试自定义 LLM 和 PandasAI 集成
"""

import os
import pandas as pd
import requests
from dotenv import load_dotenv
from pandasai import SmartDataframe
from pandasai.llm import LLM

# 加载环境变量
load_dotenv()

# 自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
class QwenLLM(LLM):
    """
    自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
    """
    def __init__(self, api_key: str, api_base: str, model: str = "qwen-plus"):
        self.api_key = api_key
        self.api_base = api_base
        self.model = model

    @property
    def type(self) -> str:
        return "qwen"

    def call(self, instruction: str, value: str, suffix: str = "") -> str:
        """
        调用阿里云百炼 OpenAI 兼容 API
        """
        prompt = f"{instruction}\n{value}{suffix}"
        
        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful data analysis assistant. Generate Python code to answer questions about data."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                return content
            else:
                return f"API 返回格式错误: {result}"
                
        except requests.exceptions.RequestException as e:
            return f"API 调用错误: {e}"
        except (KeyError, IndexError) as e:
            return f"响应解析错误: {e}. 响应内容: {response.text if 'response' in locals() else 'No response'}"

def test_custom_llm():
    """测试自定义 LLM 和 PandasAI"""
    print("🔧 测试自定义 LLM 和 PandasAI 集成...")
    
    # 检查环境变量
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    print(f"✅ API Key: {'已配置' if api_key else '❌ 未配置'}")
    print(f"✅ API Base: {api_base}")
    print(f"✅ Model: {model}")
    
    if not api_key or not api_base:
        print("❌ 配置不完整，请检查 .env 文件")
        return False
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            '产品': ['手机', '电脑', '平板'],
            '销量': [100, 50, 75],
            '价格': [3000, 8000, 2000]
        })
        
        print("📊 创建测试数据...")
        print(test_data)
        
        # 初始化自定义 LLM
        llm = QwenLLM(api_key=api_key, api_base=api_base, model=model)
        
        print("🤖 初始化自定义 LLM...")
        
        # 创建 SmartDataframe
        smart_df = SmartDataframe(test_data, config={"llm": llm})
        
        print("🧠 测试简单查询...")
        
        # 测试简单查询
        result = smart_df.chat("总销量是多少？")
        print(f"📈 查询结果: {result}")
        
        print("✅ 自定义 LLM 测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 自定义 LLM 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_custom_llm()
