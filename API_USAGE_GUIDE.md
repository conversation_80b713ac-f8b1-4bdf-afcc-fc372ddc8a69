# PandasAI 标准化API使用指南

## 概述

本项目实现了基于API中间层的PandasAI集成与标准化方案，解决了PandasAI动态输出的问题，为前端提供了可预测的、统一的API响应格式。

## API端点

### 1. 标准化API端点 (推荐)

**端点**: `POST /api/query`

**请求格式**: JSON

```json
{
  "query": "您的数据分析问题",
  "data": [
    {"列1": "值1", "列2": "值2"},
    {"列1": "值3", "列2": "值4"}
  ]
}
```

或者使用文件URL:

```json
{
  "query": "您的数据分析问题",
  "file_url": "https://example.com/data.csv"
}
```

### 2. 文件上传端点 (兼容性)

**端点**: `POST /query`

**请求格式**: FormData

- `file`: CSV或Excel文件
- `query`: 分析问题文本

## 标准化响应格式

所有API响应都遵循以下统一格式：

```json
{
  "status": "success" | "error",
  "resultType": "table" | "text" | "plot" | "raw_json" | "error",
  "data": { ... },
  "errorDetails": null | {
    "code": "错误代码",
    "message": "错误描述"
  }
}
```

### 响应类型详解

#### 1. 表格数据 (resultType: "table")

```json
{
  "status": "success",
  "resultType": "table",
  "data": {
    "columns": ["列名1", "列名2", "列名3"],
    "data": [
      ["行1值1", "行1值2", "行1值3"],
      ["行2值1", "行2值2", "行2值3"]
    ],
    "metadata": {
      "total_rows": 1000,
      "displayed_rows": 100,
      "truncated": true,
      "columns_count": 3
    }
  },
  "errorDetails": null
}
```

#### 2. 文本答案 (resultType: "text")

```json
{
  "status": "success",
  "resultType": "text",
  "data": {
    "content": "总销售额为 15,000 元"
  },
  "errorDetails": null
}
```

#### 3. 图表 (resultType: "plot")

```json
{
  "status": "success",
  "resultType": "plot",
  "data": {
    "image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "caption": "Generated Chart"
  },
  "errorDetails": null
}
```

#### 4. JSON数据 (resultType: "raw_json")

```json
{
  "status": "success",
  "resultType": "raw_json",
  "data": {
    "任意": "JSON数据结构"
  },
  "errorDetails": null
}
```

#### 5. 错误响应 (status: "error")

```json
{
  "status": "error",
  "resultType": "error",
  "data": null,
  "errorDetails": {
    "code": "ValueError",
    "message": "不支持的文件格式"
  }
}
```

## 前端集成示例

### JavaScript/React 示例

```javascript
async function queryData(query, data) {
  try {
    const response = await fetch('/api/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ query, data })
    });

    const result = await response.json();

    if (result.status === 'success') {
      switch (result.resultType) {
        case 'table':
          renderTable(result.data);
          break;
        case 'text':
          renderText(result.data.content);
          break;
        case 'plot':
          renderImage(result.data.image, result.data.caption);
          break;
        case 'raw_json':
          renderJson(result.data);
          break;
      }
    } else {
      showError(result.errorDetails.message);
    }
  } catch (error) {
    showError(`网络错误: ${error.message}`);
  }
}

function renderTable(data) {
  const { columns, data: rows, metadata } = data;
  
  let html = '<table><thead><tr>';
  columns.forEach(col => {
    html += `<th>${col}</th>`;
  });
  html += '</tr></thead><tbody>';
  
  rows.forEach(row => {
    html += '<tr>';
    row.forEach(cell => {
      html += `<td>${cell}</td>`;
    });
    html += '</tr>';
  });
  
  html += '</tbody></table>';
  
  if (metadata && metadata.truncated) {
    html += `<p>显示前 ${metadata.displayed_rows} 行，共 ${metadata.total_rows} 行</p>`;
  }
  
  document.getElementById('result').innerHTML = html;
}

function renderText(content) {
  document.getElementById('result').textContent = content;
}

function renderImage(imageData, caption) {
  document.getElementById('result').innerHTML = `
    <p>${caption}</p>
    <img src="${imageData}" alt="Generated Chart" style="max-width: 100%;" />
  `;
}

function renderJson(data) {
  document.getElementById('result').innerHTML = `
    <pre>${JSON.stringify(data, null, 2)}</pre>
  `;
}

function showError(message) {
  document.getElementById('result').innerHTML = `
    <div class="error">错误: ${message}</div>
  `;
}
```

### Python 客户端示例

```python
import requests
import json

def query_pandas_ai(query, data):
    """
    调用PandasAI标准化API
    
    Args:
        query (str): 数据分析问题
        data (list): 数据列表，每个元素是一个字典
    
    Returns:
        dict: 标准化的API响应
    """
    url = "http://localhost:5001/api/query"
    
    payload = {
        "query": query,
        "data": data
    }
    
    try:
        response = requests.post(
            url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        response.raise_for_status()
        return response.json()
        
    except requests.exceptions.RequestException as e:
        return {
            "status": "error",
            "resultType": "error",
            "data": None,
            "errorDetails": {
                "code": "NetworkError",
                "message": str(e)
            }
        }

# 使用示例
data = [
    {"产品": "A", "销售额": 1000, "销量": 50},
    {"产品": "B", "销售额": 1500, "销量": 75},
    {"产品": "C", "销售额": 800, "销量": 40}
]

result = query_pandas_ai("总销售额是多少？", data)

if result["status"] == "success":
    if result["resultType"] == "text":
        print(f"答案: {result['data']['content']}")
    elif result["resultType"] == "table":
        print("表格数据:")
        table_data = result["data"]
        print(f"列: {table_data['columns']}")
        print(f"行数: {len(table_data['data'])}")
else:
    print(f"错误: {result['errorDetails']['message']}")
```

## 测试

运行测试脚本验证API功能：

```bash
# 启动Flask应用
python app.py

# 在另一个终端运行测试
python test_standardized_api.py
```

## 优势

1. **前后端解耦**: 前端无需关心PandasAI的内部实现
2. **可预测性**: 统一的响应格式，便于前端开发
3. **可扩展性**: 易于添加新的结果类型
4. **错误处理**: 标准化的错误响应格式
5. **向后兼容**: 保留原有的文件上传接口

## 注意事项

1. 大型数据集会被截断（默认1000行）
2. 图表以Base64格式嵌入响应中
3. 临时文件会自动清理
4. API调用有超时限制（30秒）
