import os
import pandas as pd
import requests
import ast
import re
import json
import base64
from typing import Dict, Any, Union
from flask import Flask, request, jsonify, render_template
from dotenv import load_dotenv  # 用于加载 .env 文件

from pandasai import SmartDataframe
from pandasai.llm import LLM

# 在访问环境变量之前，加载 .env 文件中的内容
load_dotenv()

# 从 .env 文件或系统环境变量中加载配置
API_KEY = os.environ.get("DASHSCOPE_API_KEY")
API_BASE = os.environ.get("OPENAI_API_BASE")
MODEL = os.environ.get("LLM_MODEL", "qwen-plus")
TEMPERATURE = float(os.environ.get("LLM_TEMPERATURE", "0.1"))
MAX_TOKENS = int(os.environ.get("LLM_MAX_TOKENS", "4000"))

if not API_KEY:
    raise ValueError("未找到 DASHSCOPE_API_KEY。请确保您的 .env 文件已创建并包含该变量。")
if not API_BASE:
    raise ValueError("未找到 OPENAI_API_BASE。请确保您的 .env 文件已创建并包含该变量。")


class QueryValidator:
    """
    查询验证和引导模块 - 检测查询是否与数据相关并提供引导
    """

    @staticmethod
    def validate_query_relevance(query: str, df_columns: list) -> Dict[str, Any]:
        """
        验证查询是否与数据相关

        Args:
            query: 用户查询
            df_columns: 数据框的列名列表

        Returns:
            验证结果字典
        """
        query_lower = query.lower()

        # 数据分析相关关键词
        data_keywords = [
            '总和', '求和', '平均', '最大', '最小', '统计', '分析', '计算',
            '显示', '查看', '列出', '排序', '筛选', '过滤', '分组',
            '图表', '柱状图', '折线图', '饼图', '散点图', '可视化',
            '销售', '销量', '价格', '数量', '金额', '收入', '利润',
            'sum', 'mean', 'average', 'max', 'min', 'count', 'group',
            'chart', 'plot', 'graph', 'show', 'display', 'calculate'
        ]

        # 检查是否包含列名
        column_mentioned = any(col in query for col in df_columns)

        # 检查是否包含数据分析关键词
        keyword_mentioned = any(keyword in query_lower for keyword in data_keywords)

        # 非数据相关的查询模式
        irrelevant_patterns = [
            '天气', '新闻', '股票', '今天', '明天', '时间', '日期',
            '你好', 'hello', '谢谢', 'thank', '再见', 'bye',
            '什么是', '如何', '为什么', '怎么样', '介绍',
            '编程', '代码', '算法', '技术', '软件'
        ]

        irrelevant_mentioned = any(pattern in query_lower for pattern in irrelevant_patterns)

        # 判断相关性
        is_relevant = (column_mentioned or keyword_mentioned) and not irrelevant_mentioned

        if not is_relevant:
            return {
                "is_relevant": False,
                "suggestion": QueryValidator._generate_suggestion(df_columns),
                "guidance_type": "data_focus"
            }

        return {"is_relevant": True}

    @staticmethod
    def _generate_suggestion(df_columns: list) -> str:
        """生成针对性的建议"""
        if not df_columns:
            return "请上传包含数据的文件，然后询问相关的数据分析问题。"

        suggestions = [
            f"您可以尝试询问关于数据的问题，例如：",
            f"• '计算{df_columns[0]}的总和'",
            f"• '显示{df_columns[0]}最高的记录'",
            f"• '按{df_columns[0]}分组统计'",
            f"• '绘制{df_columns[0]}的图表'",
            f"",
            f"您的数据包含以下列：{', '.join(df_columns)}",
            f"请基于这些数据列提出分析问题。"
        ]

        return '\n'.join(suggestions)


class ResponseStandardizer:
    """
    响应标准化模块 - 将PandasAI的动态输出转换为标准化的API响应格式
    """

    @staticmethod
    def standardize_response(raw_result: Any, error: Exception = None) -> Dict[str, Any]:
        """
        将PandasAI的原始结果标准化为统一的API响应格式

        Args:
            raw_result: PandasAI返回的原始结果
            error: 如果有错误发生，传入异常对象

        Returns:
            标准化的响应字典
        """
        if error:
            return {
                "status": "error",
                "resultType": "error",
                "data": None,
                "errorDetails": {
                    "code": type(error).__name__,
                    "message": str(error)
                }
            }

        try:
            # 处理DataFrame类型
            if isinstance(raw_result, pd.DataFrame):
                return ResponseStandardizer._handle_dataframe(raw_result)

            # 处理Series类型
            elif isinstance(raw_result, pd.Series):
                return ResponseStandardizer._handle_series(raw_result)

            # 处理字符串类型（可能是图表路径或文本答案）
            elif isinstance(raw_result, str):
                return ResponseStandardizer._handle_string(raw_result)

            # 处理数值类型
            elif isinstance(raw_result, (int, float)):
                return ResponseStandardizer._handle_numeric(raw_result)

            # 处理字典类型（可能是图表信息）
            elif isinstance(raw_result, dict):
                return ResponseStandardizer._handle_dict(raw_result)

            # 处理其他类型
            else:
                return {
                    "status": "success",
                    "resultType": "text",
                    "data": {"content": str(raw_result)},
                    "errorDetails": None
                }

        except Exception as e:
            return {
                "status": "error",
                "resultType": "error",
                "data": None,
                "errorDetails": {
                    "code": "StandardizationError",
                    "message": f"响应标准化失败: {str(e)}"
                }
            }

    @staticmethod
    def _handle_dataframe(df: pd.DataFrame) -> Dict[str, Any]:
        """处理DataFrame类型的结果"""
        try:
            # 限制返回的行数，避免响应过大
            max_rows = 1000
            if len(df) > max_rows:
                df_limited = df.head(max_rows)
                truncated = True
            else:
                df_limited = df
                truncated = False

            # 转换为JSON格式，使用'split'格式便于前端处理
            json_data = df_limited.to_json(orient="split", index=False, date_format='iso')
            parsed_data = json.loads(json_data)

            # 添加元数据
            parsed_data["metadata"] = {
                "total_rows": len(df),
                "displayed_rows": len(df_limited),
                "truncated": truncated,
                "columns_count": len(df.columns)
            }

            return {
                "status": "success",
                "resultType": "table",
                "data": parsed_data,
                "errorDetails": None
            }
        except Exception as e:
            return ResponseStandardizer.standardize_response(None, e)

    @staticmethod
    def _handle_series(series: pd.Series) -> Dict[str, Any]:
        """处理Series类型的结果"""
        try:
            # 如果Series只有一个值，作为单一数值处理
            if len(series) == 1:
                return ResponseStandardizer._handle_numeric(series.iloc[0])

            # 否则转换为表格格式
            df = series.to_frame()
            return ResponseStandardizer._handle_dataframe(df)
        except Exception as e:
            return ResponseStandardizer.standardize_response(None, e)

    @staticmethod
    def _handle_string(text: str) -> Dict[str, Any]:
        """处理字符串类型的结果"""
        try:
            # 检查是否是base64编码的图片
            if text.startswith('data:image/'):
                return {
                    "status": "success",
                    "resultType": "plot",
                    "data": {
                        "image": text,
                        "caption": "Generated Chart"
                    },
                    "errorDetails": None
                }

            # 检查是否是文件路径（图表文件）
            elif text.endswith(('.png', '.jpg', '.jpeg', '.svg')):
                return ResponseStandardizer._handle_chart_file(text)

            # 普通文本答案
            else:
                return {
                    "status": "success",
                    "resultType": "text",
                    "data": {"content": text},
                    "errorDetails": None
                }
        except Exception as e:
            return ResponseStandardizer.standardize_response(None, e)

    @staticmethod
    def _handle_numeric(value: Union[int, float]) -> Dict[str, Any]:
        """处理数值类型的结果"""
        return {
            "status": "success",
            "resultType": "text",
            "data": {"content": str(value)},
            "errorDetails": None
        }

    @staticmethod
    def _handle_dict(data: dict) -> Dict[str, Any]:
        """处理字典类型的结果"""
        try:
            # 检查是否是图表信息
            if "type" in data and data["type"] == "plot":
                if "value" in data:
                    return ResponseStandardizer._handle_chart_file(data["value"])

            # 其他字典数据作为JSON返回
            return {
                "status": "success",
                "resultType": "raw_json",
                "data": data,
                "errorDetails": None
            }
        except Exception as e:
            return ResponseStandardizer.standardize_response(None, e)

    @staticmethod
    def _handle_chart_file(file_path: str) -> Dict[str, Any]:
        """处理图表文件，转换为base64编码"""
        try:
            if os.path.exists(file_path):
                with open(file_path, "rb") as img_file:
                    base64_string = base64.b64encode(img_file.read()).decode("utf-8")

                # 根据文件扩展名确定MIME类型
                if file_path.lower().endswith('.png'):
                    mime_type = "image/png"
                elif file_path.lower().endswith(('.jpg', '.jpeg')):
                    mime_type = "image/jpeg"
                elif file_path.lower().endswith('.svg'):
                    mime_type = "image/svg+xml"
                else:
                    mime_type = "image/png"  # 默认

                return {
                    "status": "success",
                    "resultType": "plot",
                    "data": {
                        "image": f"data:{mime_type};base64,{base64_string}",
                        "caption": "Generated Chart"
                    },
                    "errorDetails": None
                }
            else:
                raise FileNotFoundError(f"图表文件不存在: {file_path}")

        except Exception as e:
            return ResponseStandardizer.standardize_response(None, e)


def fix_syntax_errors(code):
    """修复常见的语法错误"""
    if not code or not code.strip():
        return "result = df.describe()"

    lines = code.split('\n')
    fixed_lines = []

    for line in lines:
        # 移除不匹配的大括号
        if '}' in line and '{' not in line:
            line = line.replace('}', '')

        # 修复不完整的赋值语句
        if line.strip().startswith('result =') and line.strip().endswith('}'):
            line = line.replace('}', '')

        # 移除空的大括号
        line = line.replace('{}', '')

        # 修复不匹配的括号
        open_parens = line.count('(')
        close_parens = line.count(')')
        if close_parens > open_parens:
            # 移除多余的右括号
            diff = close_parens - open_parens
            for _ in range(diff):
                line = line.replace(')', '', 1)
        elif open_parens > close_parens:
            # 添加缺失的右括号
            diff = open_parens - close_parens
            line += ')' * diff

        # 修复不完整的字符串
        if line.count('"') % 2 != 0:
            line += '"'
        if line.count("'") % 2 != 0:
            line += "'"

        fixed_lines.append(line)

    fixed_code = '\n'.join(fixed_lines)

    # 确保有 result 变量
    if 'result =' not in fixed_code:
        fixed_code += '\nresult = df.describe()'

    return fixed_code


def validate_and_fix_code(code):
    """验证并修复代码语法"""
    if not code or not code.strip():
        return "result = df.describe()"

    # 首先尝试解析原始代码
    try:
        ast.parse(code)
        return code  # 代码语法正确
    except SyntaxError:
        # 尝试修复语法错误
        fixed_code = fix_syntax_errors(code)

        try:
            ast.parse(fixed_code)
            return fixed_code  # 修复成功
        except SyntaxError:
            # 如果还是有问题，返回一个安全的默认代码
            return "result = df.describe()"


# 自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
class QwenLLM(LLM):
    """
    自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
    """
    def __init__(self, api_key: str, api_base: str, model: str = "qwen-plus"):
        self.api_key = api_key
        self.api_base = api_base
        self.model = model

    @property
    def type(self) -> str:
        return "qwen"

    def generate_code(self, query: str, context) -> str:
        """
        重写 generate_code 方法，确保返回正确格式的代码
        """
        # 构建专门用于代码生成的提示
        prompt = f"""
Based on the following data analysis question, generate Python pandas code to answer it.

Question: {query}

Data context: {context}

Requirements:
1. Use pandas operations (df is the dataframe variable)
2. The final answer must be stored in a variable called 'result'
3. Generate ONLY executable Python code, no markdown formatting
4. Do not include ```python or ``` tags
5. Make sure the last line assigns the final answer to 'result'
6. Ensure all brackets, parentheses, and braces are properly matched
7. Do not use curly braces {{}} unless absolutely necessary for dictionaries
8. Use simple variable assignments, avoid complex expressions

Example:
# Calculate total sales
df['销售额'] = df['销量'] * df['价格']
total_sales = df['销售额'].sum()
result = total_sales

Please generate the appropriate pandas code (code only, no explanations):
"""

        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a Python data analysis expert. Generate ONLY clean, executable pandas code. No markdown formatting, no explanations, just pure Python code. Always ensure the final result is stored in a variable called 'result'. Avoid using curly braces {} unless absolutely necessary. Ensure all brackets and parentheses are properly matched."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": TEMPERATURE,
            "max_tokens": MAX_TOKENS
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=120)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"].strip()

                # 清理响应，移除任何 markdown 格式
                if "```python" in content:
                    # 提取代码块中的内容
                    code_match = re.search(r'```python\s*(.*?)\s*```', content, re.DOTALL)
                    if code_match:
                        content = code_match.group(1).strip()
                elif "```" in content:
                    # 移除任何代码块标记
                    content = re.sub(r'```.*?\n', '', content)
                    content = re.sub(r'```', '', content)
                    content = content.strip()

                # 如果内容不像代码，生成一个基本的代码
                if not any(keyword in content for keyword in ['df', '=', 'result']):
                    content = "result = df.sum().sum()  # 计算所有数值列的总和"

                # 确保代码的最后一行是 result =
                lines = content.split('\n')
                if lines and 'result =' not in lines[-1]:
                    # 如果最后一行不是 result = ，尝试修复
                    if len(lines) > 1:
                        # 多行代码，确保最后一行赋值给 result
                        last_line = lines[-1].strip()
                        if '=' in last_line and 'result' not in last_line:
                            # 最后一行是赋值，但不是给 result
                            var_name = last_line.split('=')[0].strip()
                            lines[-1] = f"result = {var_name}"
                        elif not last_line.startswith('result'):
                            # 最后一行不是赋值，添加 result =
                            lines.append(f"result = {last_line}")
                    else:
                        # 单行代码
                        if content.strip() and not content.strip().startswith('result'):
                            content = f"result = {content.strip()}"
                    content = '\n'.join(lines)

                # 验证并修复代码语法
                content = validate_and_fix_code(content)

                return content
            else:
                # 如果 API 调用失败，返回一个默认的代码
                return validate_and_fix_code("result = df.describe()")

        except Exception as e:
            # 如果出现任何错误，返回一个安全的默认代码
            return validate_and_fix_code(f"result = df.describe()  # API调用错误: {str(e)[:50]}")

    def call(self, instruction: str, value: str, suffix: str = "") -> str:
        """
        调用阿里云百炼 OpenAI 兼容 API
        """
        prompt = f"{instruction}\n{value}{suffix}"

        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 构建更详细的系统提示，确保模型生成符合 PandasAI 期望的代码格式
        system_content = """You are a helpful data analysis assistant.
You must generate Python code to answer questions about data.
Always wrap your Python code in ```python and ``` tags.
The code should use pandas operations and return the result.
For example:
```python
result = df['column'].sum()
```
Make sure to include proper Python code that can be executed."""

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_content
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": TEMPERATURE,
            "max_tokens": MAX_TOKENS
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=120)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]

                # 如果响应中没有代码块，尝试添加代码块标记
                if "```python" not in content and "```" not in content:
                    # 检查是否包含看起来像 Python 代码的内容
                    if any(keyword in content.lower() for keyword in ['df[', 'df.', 'sum()', 'mean()', 'count()', 'groupby']):
                        content = f"```python\n{content}\n```"
                    else:
                        # 如果没有明显的代码，尝试生成一个简单的代码
                        content = f"```python\n# 基于问题生成的代码\nresult = df.sum().sum() if hasattr(df, 'sum') else 'Unable to calculate'\n```\n\n分析说明：{content}"

                return content
            else:
                return f"API 返回格式错误: {result}"

        except requests.exceptions.RequestException as e:
            return f"API 调用错误: {e}"
        except (KeyError, IndexError) as e:
            return f"响应解析错误: {e}. 响应内容: {response.text if 'response' in locals() else 'No response'}"


# 初始化 Flask 应用
app = Flask(__name__)
UPLOAD_FOLDER = 'uploads'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER


@app.route('/')
def index():
    """渲染前端主页"""
    return render_template('index.html')


@app.route('/demo')
def demo():
    """渲染API演示页面"""
    return render_template('api_demo.html')


@app.route('/query', methods=['POST'])
def handle_query():
    """处理来自前端的数据分析请求 - 使用标准化响应格式"""
    # 验证请求参数
    if 'file' not in request.files or 'query' not in request.form:
        error_response = ResponseStandardizer.standardize_response(
            None,
            ValueError("请求中缺少文件或查询问题。")
        )
        return jsonify(error_response), 400

    file = request.files['file']
    query = request.form['query']

    if file.filename == '':
        error_response = ResponseStandardizer.standardize_response(
            None,
            ValueError("未选择任何文件。")
        )
        return jsonify(error_response), 400

    filepath = None  # 初始化 filepath 变量
    try:
        # 将上传的文件保存到服务器
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
        file.save(filepath)

        # 根据文件扩展名读取数据到 pandas DataFrame
        if filepath.endswith('.csv'):
            df = pd.read_csv(filepath)
        elif filepath.endswith('.xlsx'):
            df = pd.read_excel(filepath)
        else:
            raise ValueError("不支持的文件格式，请上传 CSV 或 Excel 文件。")

        # 记录数据基本信息
        app.logger.info(f"数据文件加载成功: {file.filename}, 形状: {df.shape}")

        # 验证查询相关性
        validation_result = QueryValidator.validate_query_relevance(query, df.columns.tolist())

        if not validation_result["is_relevant"]:
            # 返回引导建议
            guidance_response = {
                "status": "guidance",
                "resultType": "guidance",
                "data": {
                    "message": "您的问题似乎与上传的数据不太相关。",
                    "suggestion": validation_result["suggestion"],
                    "guidance_type": validation_result["guidance_type"]
                },
                "errorDetails": None
            }
            return jsonify(guidance_response)

        # 初始化自定义的 Qwen LLM
        llm = QwenLLM(api_key=API_KEY, api_base=API_BASE, model=MODEL)

        # 确保图表保存目录存在
        charts_dir = "exports/charts"
        os.makedirs(charts_dir, exist_ok=True)

        # 使用 SmartDataframe 初始化 PandasAI
        smart_df = SmartDataframe(
            df,
            config={
                "llm": llm,
                "save_charts": True,
                "save_charts_path": charts_dir,
                "open_charts": False,
                "verbose": True  # 启用详细日志
            }
        )

        # 执行核心查询
        app.logger.info(f"开始处理查询: {query}")
        raw_response = smart_df.chat(query)
        app.logger.info(f"PandasAI原始响应类型: {type(raw_response)}")

        # 使用响应标准化模块处理结果
        standardized_response = ResponseStandardizer.standardize_response(raw_response)

        # 返回标准化的响应
        return jsonify(standardized_response)

    except Exception as e:
        # 记录并返回标准化的错误响应
        app.logger.error(f"处理查询时发生错误: {e}")
        error_response = ResponseStandardizer.standardize_response(None, e)
        return jsonify(error_response), 500
    finally:
        # 无论成功还是失败，都确保删除上传的临时文件
        if filepath and os.path.exists(filepath):
            try:
                os.remove(filepath)
                app.logger.info(f"临时文件已删除: {filepath}")
            except Exception as cleanup_error:
                app.logger.warning(f"删除临时文件失败: {cleanup_error}")


@app.route('/api/query', methods=['POST'])
def handle_api_query():
    """
    新的API端点 - 完全符合标准化响应格式
    支持JSON请求体，更适合API集成
    """
    try:
        # 解析JSON请求
        if request.is_json:
            data = request.get_json()
            query = data.get('query')
            # 对于API端点，可以支持直接传入DataFrame数据或文件URL
            if 'data' in data:
                # 直接传入数据
                df = pd.DataFrame(data['data'])
            elif 'file_url' in data:
                # 从URL加载数据
                file_url = data['file_url']
                if file_url.endswith('.csv'):
                    df = pd.read_csv(file_url)
                elif file_url.endswith('.xlsx'):
                    df = pd.read_excel(file_url)
                else:
                    raise ValueError("不支持的文件格式")
            else:
                raise ValueError("请求中必须包含 'data' 或 'file_url' 字段")
        else:
            # 兼容表单数据
            return handle_query()

        if not query:
            raise ValueError("查询问题不能为空")

        # 验证查询相关性
        validation_result = QueryValidator.validate_query_relevance(query, df.columns.tolist())

        if not validation_result["is_relevant"]:
            # 返回引导建议
            guidance_response = {
                "status": "guidance",
                "resultType": "guidance",
                "data": {
                    "message": "您的问题似乎与提供的数据不太相关。",
                    "suggestion": validation_result["suggestion"],
                    "guidance_type": validation_result["guidance_type"]
                },
                "errorDetails": None
            }
            return jsonify(guidance_response)

        # 初始化LLM和SmartDataframe
        llm = QwenLLM(api_key=API_KEY, api_base=API_BASE, model=MODEL)
        charts_dir = "exports/charts"
        os.makedirs(charts_dir, exist_ok=True)

        smart_df = SmartDataframe(
            df,
            config={
                "llm": llm,
                "save_charts": True,
                "save_charts_path": charts_dir,
                "open_charts": False,
                "verbose": True
            }
        )

        # 执行查询
        raw_response = smart_df.chat(query)

        # 标准化响应
        standardized_response = ResponseStandardizer.standardize_response(raw_response)

        return jsonify(standardized_response)

    except Exception as e:
        app.logger.error(f"API查询处理错误: {e}")
        error_response = ResponseStandardizer.standardize_response(None, e)
        return jsonify(error_response), 500


# 启动 Flask 应用
if __name__ == '__main__':
    # host='0.0.0.0' 确保服务可以从网络中的其他机器访问
    # port=5001 指定一个端口
    app.run(host='0.0.0.0', port=5001, debug=True)
