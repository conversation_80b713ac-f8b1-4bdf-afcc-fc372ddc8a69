#!/usr/bin/env python3
"""
简单测试网络连接和 API
"""

import os
import requests
from dotenv import load_dotenv

load_dotenv()

def test_connection():
    """测试网络连接"""
    print("🌐 测试网络连接...")
    
    try:
        # 先测试基本的网络连接
        response = requests.get("https://www.baidu.com", timeout=10)
        print(f"✅ 网络连接正常: {response.status_code}")
    except Exception as e:
        print(f"❌ 网络连接失败: {e}")
        return False
    
    # 测试阿里云域名连接
    try:
        response = requests.get("https://dashscope.aliyuncs.com", timeout=10)
        print(f"✅ 阿里云域名连接正常: {response.status_code}")
    except Exception as e:
        print(f"❌ 阿里云域名连接失败: {e}")
        return False
    
    return True

def test_simple_api():
    """简单的 API 测试"""
    print("\n🔧 测试阿里云百炼 API...")
    
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    
    if not api_key:
        print("❌ 未找到 API Key")
        return False
    
    try:
        url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-plus",
            "messages": [
                {"role": "user", "content": "1+1等于几？"}
            ]
        }
        
        print("🚀 发送简单请求...")
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📡 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ API 测试成功!")
            print(f"📝 回答: {content}")
            return True
        else:
            print(f"❌ API 调用失败")
            print(f"📄 响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API 测试失败: {e}")
        return False

if __name__ == "__main__":
    if test_connection():
        test_simple_api()
    else:
        print("❌ 网络连接测试失败，跳过 API 测试")
