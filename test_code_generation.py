#!/usr/bin/env python3
"""
测试代码生成功能
"""

import os
import pandas as pd
import requests
from dotenv import load_dotenv
from pandasai import SmartDataframe
from pandasai.llm import LLM

# 加载环境变量
load_dotenv()

# 自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
class QwenLLM(LLM):
    """
    自定义 LLM 类，使用阿里云百炼的 OpenAI 兼容接口
    """
    def __init__(self, api_key: str, api_base: str, model: str = "qwen-plus"):
        self.api_key = api_key
        self.api_base = api_base
        self.model = model

    @property
    def type(self) -> str:
        return "qwen"

    def generate_code(self, query: str, context) -> str:
        """
        重写 generate_code 方法，确保返回正确格式的代码
        """
        # 构建专门用于代码生成的提示
        prompt = f"""
Based on the following data analysis question, generate Python pandas code to answer it.

Question: {query}

Data context: {context}

Requirements:
1. Use pandas operations (df is the dataframe variable)
2. Return the result in a variable called 'result'
3. Generate ONLY executable Python code, no markdown formatting
4. Do not include ```python or ``` tags

Example:
result = df['column_name'].sum()

Please generate the appropriate pandas code (code only, no explanations):
"""

        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a Python data analysis expert. Generate ONLY clean, executable pandas code. No markdown formatting, no explanations, just pure Python code."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000
        }

        try:
            response = requests.post(url, headers=headers, json=data, timeout=120)
            response.raise_for_status()

            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"].strip()

                # 清理响应，移除任何 markdown 格式
                if "```python" in content:
                    # 提取代码块中的内容
                    import re
                    code_match = re.search(r'```python\s*(.*?)\s*```', content, re.DOTALL)
                    if code_match:
                        content = code_match.group(1).strip()
                elif "```" in content:
                    # 移除任何代码块标记
                    content = re.sub(r'```.*?\n', '', content)
                    content = re.sub(r'```', '', content)
                    content = content.strip()

                # 如果内容不像代码，生成一个基本的代码
                if not any(keyword in content for keyword in ['df', '=', 'result']):
                    content = "result = df.sum().sum()  # 计算所有数值列的总和"

                # 确保代码以 result = 开头或包含 result =
                if 'result =' not in content:
                    if content.strip():
                        content = f"result = {content.strip()}"
                    else:
                        content = "result = df.sum().sum()"

                return content
            else:
                # 如果 API 调用失败，返回一个默认的代码
                return "result = df.describe()"

        except Exception as e:
            # 如果出现任何错误，返回一个安全的默认代码
            return f"result = df.describe()  # API调用错误: {str(e)[:50]}"

    def call(self, instruction: str, value: str, suffix: str = "") -> str:
        """
        调用阿里云百炼 OpenAI 兼容 API
        """
        prompt = f"{instruction}\n{value}{suffix}"
        
        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建更详细的系统提示，确保模型生成符合 PandasAI 期望的代码格式
        system_content = """You are a helpful data analysis assistant. 
You must generate Python code to answer questions about data.
Always wrap your Python code in ```python and ``` tags.
The code should use pandas operations and return the result.
For example:
```python
result = df['column'].sum()
```
Make sure to include proper Python code that can be executed."""

        data = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": system_content
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                
                # 如果响应中没有代码块，尝试添加代码块标记
                if "```python" not in content and "```" not in content:
                    # 检查是否包含看起来像 Python 代码的内容
                    if any(keyword in content.lower() for keyword in ['df[', 'df.', 'sum()', 'mean()', 'count()', 'groupby']):
                        content = f"```python\n{content}\n```"
                    else:
                        # 如果没有明显的代码，尝试生成一个简单的代码
                        content = f"```python\n# 基于问题生成的代码\nresult = df.sum().sum() if hasattr(df, 'sum') else 'Unable to calculate'\n```\n\n分析说明：{content}"
                
                return content
            else:
                return f"API 返回格式错误: {result}"
                
        except requests.exceptions.RequestException as e:
            return f"API 调用错误: {e}"
        except (KeyError, IndexError) as e:
            return f"响应解析错误: {e}. 响应内容: {response.text if 'response' in locals() else 'No response'}"

def test_code_generation():
    """测试代码生成功能"""
    print("🔧 测试代码生成功能...")
    
    # 检查环境变量
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    if not api_key or not api_base:
        print("❌ 配置不完整，请检查 .env 文件")
        return False
    
    try:
        # 创建测试数据
        test_data = pd.DataFrame({
            '产品': ['手机', '电脑', '平板'],
            '销量': [100, 50, 75],
            '价格': [3000, 8000, 2000]
        })
        
        print("📊 创建测试数据...")
        print(test_data)
        
        # 初始化自定义 LLM
        llm = QwenLLM(api_key=api_key, api_base=api_base, model=model)
        
        print("🤖 初始化自定义 LLM...")
        
        # 创建 SmartDataframe
        smart_df = SmartDataframe(test_data, config={"llm": llm})
        
        print("🧠 测试复杂查询...")
        
        # 测试复杂查询
        result = smart_df.chat("总销售额是多少，请同时为我输出分析内容")
        print(f"📈 查询结果: {result}")
        
        print("✅ 代码生成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 代码生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_code_generation()
