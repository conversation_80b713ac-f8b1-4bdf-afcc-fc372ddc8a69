#!/usr/bin/env python3
"""
直接测试阿里云百炼 API 是否工作
"""

import os
import requests
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_api_direct():
    """直接测试 API 调用"""
    print("🔧 直接测试阿里云百炼 API...")
    
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    print(f"✅ API Key: {'已配置' if api_key else '❌ 未配置'}")
    print(f"✅ API Base: {api_base}")
    print(f"✅ Model: {model}")
    
    if not api_key or not api_base:
        print("❌ 配置不完整，请检查 .env 文件")
        return False
    
    try:
        # 构建请求
        url = f"{api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ],
            "temperature": 0.1,
            "max_tokens": 100
        }
        
        print("🚀 发送 API 请求...")
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print(f"📡 HTTP 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
            print(f"✅ API 调用成功!")
            print(f"📝 响应内容: {content}")
            return True
        else:
            print(f"❌ API 调用失败: {response.status_code}")
            print(f"📄 错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API 调用异常: {e}")
        return False

if __name__ == "__main__":
    test_api_direct()
