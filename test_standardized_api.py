#!/usr/bin/env python3
"""
测试标准化API响应格式的脚本
"""

import requests
import json
import pandas as pd
import os
from typing import Dict, Any

def test_standardized_api():
    """测试新的标准化API端点"""
    
    # API端点
    base_url = "http://localhost:5001"
    api_endpoint = f"{base_url}/api/query"
    
    # 创建测试数据
    test_data = {
        "产品名称": ["产品A", "产品B", "产品C", "产品D", "产品E"],
        "销售额": [1000, 1500, 800, 2000, 1200],
        "销量": [50, 75, 40, 100, 60],
        "价格": [20, 20, 20, 20, 20],
        "城市": ["北京", "上海", "广州", "深圳", "杭州"]
    }
    
    # 测试用例
    test_cases = [
        {
            "name": "文本查询 - 总销售额",
            "query": "总销售额是多少？",
            "expected_type": "text"
        },
        {
            "name": "表格查询 - 销售额排序",
            "query": "按销售额从高到低排序显示所有产品",
            "expected_type": "table"
        },
        {
            "name": "数值查询 - 平均销售额",
            "query": "计算平均销售额",
            "expected_type": "text"
        },
        {
            "name": "图表查询 - 柱状图",
            "query": "画一个显示各产品销售额的柱状图",
            "expected_type": "plot"
        },
        {
            "name": "筛选查询 - 高销售额产品",
            "query": "显示销售额大于1000的产品",
            "expected_type": "table"
        }
    ]
    
    print("🚀 开始测试标准化API...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"查询: {test_case['query']}")
        print("-" * 40)
        
        # 构建请求数据
        request_data = {
            "query": test_case["query"],
            "data": test_data
        }
        
        try:
            # 发送请求
            response = requests.post(
                api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print_test_result(result, test_case["expected_type"])
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

def print_test_result(result: Dict[str, Any], expected_type: str):
    """打印测试结果"""
    
    # 检查基本结构
    if not isinstance(result, dict):
        print(f"❌ 响应不是字典格式: {type(result)}")
        return
    
    required_fields = ["status", "resultType", "data", "errorDetails"]
    missing_fields = [field for field in required_fields if field not in result]
    
    if missing_fields:
        print(f"❌ 缺少必需字段: {missing_fields}")
        return
    
    status = result["status"]
    result_type = result["resultType"]
    data = result["data"]
    error_details = result["errorDetails"]
    
    print(f"状态: {status}")
    print(f"结果类型: {result_type}")
    
    if status == "success":
        print("✅ 请求成功")
        
        # 检查结果类型是否符合预期
        if result_type == expected_type:
            print(f"✅ 结果类型符合预期: {result_type}")
        else:
            print(f"⚠️  结果类型不符合预期: 期望 {expected_type}, 实际 {result_type}")
        
        # 根据结果类型显示数据摘要
        if result_type == "table":
            if isinstance(data, dict) and "columns" in data and "data" in data:
                rows = len(data["data"])
                cols = len(data["columns"])
                print(f"📊 表格数据: {rows} 行 x {cols} 列")
                if "metadata" in data:
                    metadata = data["metadata"]
                    print(f"   元数据: {metadata}")
            else:
                print("❌ 表格数据格式不正确")
                
        elif result_type == "text":
            if isinstance(data, dict) and "content" in data:
                content = str(data["content"])
                preview = content[:100] + "..." if len(content) > 100 else content
                print(f"📝 文本内容: {preview}")
            else:
                print("❌ 文本数据格式不正确")
                
        elif result_type == "plot":
            if isinstance(data, dict) and "image" in data:
                image_data = data["image"]
                if image_data.startswith("data:image/"):
                    print("🎨 图表数据: Base64编码图片")
                    print(f"   标题: {data.get('caption', 'N/A')}")
                else:
                    print("❌ 图片数据格式不正确")
            else:
                print("❌ 图表数据格式不正确")
                
        elif result_type == "raw_json":
            print(f"🔧 JSON数据: {type(data)}")
            
    elif status == "error":
        print("❌ 请求失败")
        if error_details:
            print(f"错误代码: {error_details.get('code', 'N/A')}")
            print(f"错误信息: {error_details.get('message', 'N/A')}")
    
    else:
        print(f"❌ 未知状态: {status}")

def test_file_upload_endpoint():
    """测试原有的文件上传端点"""
    print("\n🔄 测试文件上传端点...")
    
    # 创建测试CSV文件
    test_df = pd.DataFrame({
        "产品名称": ["产品A", "产品B", "产品C"],
        "销售额": [1000, 1500, 800],
        "销量": [50, 75, 40]
    })
    
    test_file = "test_data.csv"
    test_df.to_csv(test_file, index=False, encoding='utf-8')
    
    try:
        url = "http://localhost:5001/query"
        
        with open(test_file, 'rb') as f:
            files = {'file': (test_file, f, 'text/csv')}
            data = {'query': '总销售额是多少？'}
            
            response = requests.post(url, files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 文件上传端点测试成功")
                print_test_result(result, "text")
            else:
                print(f"❌ 文件上传端点测试失败: {response.status_code}")
                print(response.text)
                
    except Exception as e:
        print(f"❌ 文件上传端点测试异常: {e}")
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

if __name__ == "__main__":
    print("🧪 PandasAI 标准化API测试工具")
    print("请确保Flask应用正在运行 (python app.py)")
    print("=" * 60)
    
    # 测试API端点
    test_standardized_api()
    
    # 测试文件上传端点
    test_file_upload_endpoint()
