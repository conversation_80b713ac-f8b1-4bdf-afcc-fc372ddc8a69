#!/usr/bin/env python3
"""
测试语法修复功能
"""

import os
import pandas as pd
import ast
import requests
from dotenv import load_dotenv
from pandasai import SmartDataframe

# 加载环境变量
load_dotenv()

# 导入修复后的 LLM 类
from app import QwenLLM, validate_and_fix_code, fix_syntax_errors

def test_syntax_validation():
    """测试语法验证和修复功能"""
    print("🔧 测试语法验证和修复功能...")
    
    # 测试用例：包含各种语法错误的代码
    test_cases = [
        {
            "name": "不匹配的大括号",
            "code": "result = })",
            "expected_valid": True
        },
        {
            "name": "多余的右括号",
            "code": "result = df.sum()))",
            "expected_valid": True
        },
        {
            "name": "缺少右括号",
            "code": "result = df.sum(",
            "expected_valid": True
        },
        {
            "name": "不完整的字符串",
            "code": 'result = "hello',
            "expected_valid": True
        },
        {
            "name": "正常代码",
            "code": "result = df.sum()",
            "expected_valid": True
        },
        {
            "name": "复杂的正常代码",
            "code": """
df['销售额'] = df['销量'] * df['价格']
total_sales = df['销售额'].sum()
result = total_sales
""",
            "expected_valid": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 测试: {test_case['name']}")
        print(f"原始代码: '{test_case['code']}'")
        
        # 测试原始代码语法
        try:
            ast.parse(test_case['code'])
            print("✅ 原始代码语法正确")
            original_valid = True
        except SyntaxError as e:
            print(f"❌ 原始代码语法错误: {e}")
            original_valid = False
        
        # 测试修复后的代码
        fixed_code = validate_and_fix_code(test_case['code'])
        print(f"修复后代码: '{fixed_code}'")
        
        try:
            ast.parse(fixed_code)
            print("✅ 修复后代码语法正确")
            fixed_valid = True
        except SyntaxError as e:
            print(f"❌ 修复后代码仍有语法错误: {e}")
            fixed_valid = False
        
        if fixed_valid == test_case['expected_valid']:
            print("✅ 测试通过")
        else:
            print("❌ 测试失败")

def test_problematic_queries():
    """测试有问题的查询"""
    print("\n🔧 测试有问题的查询...")
    
    # 检查环境变量
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    if not api_key or not api_base:
        print("❌ 配置不完整，跳过 API 测试")
        return
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '产品': ['手机', '电脑', '平板'],
        '销量': [100, 50, 75],
        '价格': [3000, 8000, 2000]
    })
    
    print("📊 测试数据:")
    print(test_data)
    
    # 初始化修复后的 LLM
    llm = QwenLLM(api_key=api_key, api_base=api_base, model=model)
    
    # 创建 SmartDataframe
    smart_df = SmartDataframe(test_data, config={"llm": llm})
    
    # 测试有问题的查询
    problematic_queries = [
        "总销售额有多少，并分析构成",
        "计算每个产品的销售额占比",
        "显示销量最高的产品",
        "分析价格分布情况"
    ]
    
    for query in problematic_queries:
        print(f"\n🧠 测试查询: '{query}'")
        try:
            result = smart_df.chat(query)
            print(f"✅ 查询成功")
            print(f"📈 结果: {result}")
        except Exception as e:
            print(f"❌ 查询失败: {e}")
            
            # 如果是语法错误，尝试直接测试代码生成
            if "SyntaxError" in str(e) or "unmatched" in str(e):
                print("🔧 尝试直接测试代码生成...")
                try:
                    generated_code = llm.generate_code(query, "DataFrame with columns: 产品, 销量, 价格")
                    print(f"生成的代码: '{generated_code}'")
                    
                    # 验证生成的代码
                    try:
                        ast.parse(generated_code)
                        print("✅ 生成的代码语法正确")
                    except SyntaxError as syntax_error:
                        print(f"❌ 生成的代码仍有语法错误: {syntax_error}")
                        
                except Exception as gen_error:
                    print(f"❌ 代码生成失败: {gen_error}")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔧 测试边界情况...")
    
    edge_cases = [
        "",  # 空字符串
        "   ",  # 只有空格
        "# 只有注释",  # 只有注释
        "df",  # 不完整的表达式
        "result =",  # 不完整的赋值
        "result = {",  # 不完整的字典
        "result = [",  # 不完整的列表
        "result = (",  # 不完整的元组
    ]
    
    for case in edge_cases:
        print(f"\n📝 测试边界情况: '{case}'")
        fixed_code = validate_and_fix_code(case)
        print(f"修复后: '{fixed_code}'")
        
        try:
            ast.parse(fixed_code)
            print("✅ 修复成功")
        except SyntaxError as e:
            print(f"❌ 修复失败: {e}")

if __name__ == "__main__":
    test_syntax_validation()
    test_problematic_queries()
    test_edge_cases()
