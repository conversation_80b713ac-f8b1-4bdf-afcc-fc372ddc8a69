#!/usr/bin/env python3
"""
测试用户引导功能的脚本
"""

import requests
import json
import pandas as pd

def test_user_guidance():
    """测试用户引导功能"""
    
    # API端点
    base_url = "http://localhost:5001"
    api_endpoint = f"{base_url}/api/query"
    
    # 创建测试数据
    test_data = {
        "产品名称": ["手机", "电脑", "平板", "耳机", "键盘"],
        "销售额": [15000, 25000, 8000, 3000, 1500],
        "销量": [50, 25, 40, 100, 75],
        "价格": [300, 1000, 200, 30, 20],
        "类别": ["电子产品", "电子产品", "电子产品", "配件", "配件"]
    }
    
    # 测试用例：相关和不相关的查询
    test_cases = [
        {
            "name": "相关查询 - 销售额统计",
            "query": "计算总销售额",
            "expected_status": "success"
        },
        {
            "name": "相关查询 - 产品分析",
            "query": "显示销量最高的产品",
            "expected_status": "success"
        },
        {
            "name": "不相关查询 - 天气询问",
            "query": "今天天气怎么样？",
            "expected_status": "guidance"
        },
        {
            "name": "不相关查询 - 一般问候",
            "query": "你好，请介绍一下自己",
            "expected_status": "guidance"
        },
        {
            "name": "不相关查询 - 技术问题",
            "query": "什么是机器学习？",
            "expected_status": "guidance"
        },
        {
            "name": "边界查询 - 包含列名但不相关",
            "query": "产品名称这个词的英文怎么说？",
            "expected_status": "guidance"
        },
        {
            "name": "相关查询 - 包含数据关键词",
            "query": "按类别分组统计平均价格",
            "expected_status": "success"
        }
    ]
    
    print("🧭 开始测试用户引导功能...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['name']}")
        print(f"查询: {test_case['query']}")
        print(f"预期状态: {test_case['expected_status']}")
        print("-" * 40)
        
        # 构建请求数据
        request_data = {
            "query": test_case["query"],
            "data": test_data
        }
        
        try:
            # 发送请求
            response = requests.post(
                api_endpoint,
                json=request_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print_guidance_test_result(result, test_case["expected_status"])
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求异常: {e}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析错误: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 用户引导功能测试完成")

def print_guidance_test_result(result, expected_status):
    """打印引导测试结果"""
    
    status = result.get("status", "unknown")
    result_type = result.get("resultType", "unknown")
    
    # 检查状态是否符合预期
    if status == expected_status:
        print(f"✅ 状态正确: {status}")
    else:
        print(f"❌ 状态错误: 期望 {expected_status}, 实际 {status}")
    
    print(f"📊 结果类型: {result_type}")
    
    # 根据不同状态显示详细信息
    if status == "guidance":
        data = result.get("data", {})
        print(f"💡 引导消息: {data.get('message', 'N/A')}")
        print(f"📝 建议内容:")
        suggestion = data.get('suggestion', 'N/A')
        # 只显示建议的前几行
        suggestion_lines = suggestion.split('\n')[:5]
        for line in suggestion_lines:
            print(f"   {line}")
        if len(suggestion.split('\n')) > 5:
            print("   ...")
            
    elif status == "success":
        data = result.get("data", {})
        if result_type == "text":
            print(f"📄 文本结果: {data.get('content', 'N/A')}")
        elif result_type == "table":
            metadata = data.get('metadata', {})
            print(f"📊 表格结果: {metadata.get('displayed_rows', 'N/A')} 行")
        else:
            print(f"📋 其他结果类型: {result_type}")
            
    elif status == "error":
        error_details = result.get("errorDetails", {})
        print(f"❌ 错误: {error_details.get('message', 'N/A')}")
    
    print()

if __name__ == "__main__":
    test_user_guidance()
