<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PandasAI with Qwen-max</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            display: flex;
            height: 100vh;
            margin: 0;
            background-color: #f4f5f7;
        }
        .container {
            display: flex;
            width: 100%;
        }
        .panel {
            padding: 25px;
            box-sizing: border-box;
            height: 100vh;
            overflow-y: auto;
        }
        .left-panel {
            width: 40%;
            border-right: 1px solid #dcdfe6;
            background-color: #ffffff;
        }
        .right-panel {
            width: 60%;
            background-color: #f9fafb;
        }
        h2 {
            color: #303133;
            border-bottom: 2px solid #409eff;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 25px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            color: #606266;
            font-weight: bold;
        }
        textarea, input[type="file"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        textarea {
            resize: vertical;
            min-height: 150px;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #409eff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button:disabled {
            background-color: #a0cfff;
            cursor: not-allowed;
        }
        #output-container {
            background-color: #ffffff;
            padding: 20px;
            border-radius: 4px;
            border: 1px solid #ebeef5;
            min-height: calc(100% - 100px);
            color: #303133;
            overflow-x: auto;
        }
        #output {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .result-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .result-table th, .result-table td {
            border: 1px solid #dcdfe6;
            padding: 8px 12px;
            text-align: left;
        }
        .result-table th {
            background-color: #f5f7fa;
            font-weight: bold;
            color: #303133;
        }
        .result-table tr:nth-child(even) {
            background-color: #fafafa;
        }
        .result-metadata {
            background-color: #f0f9ff;
            border: 1px solid #bfdbfe;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            color: #1e40af;
        }
        .error-message {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 4px;
            padding: 15px;
            color: #dc2626;
            margin-top: 10px;
        }
        .success-message {
            background-color: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 4px;
            padding: 15px;
            color: #166534;
            margin-top: 10px;
        }
        #spinner {
            display: none;
            text-align: center;
            padding: 30px;
            font-size: 18px;
            color: #409eff;
        }
    </style>
</head>
<body>

<div class="container">
    <div class="panel left-panel">
        <h2>控制面板</h2>
        <form id="query-form">
            <div class="form-group">
                <label for="file-upload">1. 上传数据文件 (CSV 或 Excel)</label>
                <input type="file" id="file-upload" name="file" accept=".csv, .xlsx" required>
            </div>
            <div class="form-group">
                <label for="query-text">2. 输入您的问题</label>
                <textarea id="query-text" name="query" placeholder="示例：总销售额是多少？&#10;或者，按城市绘制销售额的条形图" required></textarea>
            </div>
            <button type="submit" id="submit-button">提交分析</button>
        </form>
    </div>
    <div class="panel right-panel">
        <h2>分析结果</h2>
        <div id="spinner">🧠 正在调用大模型分析中，请稍候...</div>
        <div id="output-container">
            <pre id="output"></pre>
        </div>
    </div>
</div>

<script>
    const form = document.getElementById('query-form');
    const outputDiv = document.getElementById('output');
    const submitButton = document.getElementById('submit-button');
    const spinner = document.getElementById('spinner');

    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        
        const formData = new FormData(form);
        
        if (!formData.get('file').name || !formData.get('query').trim()) {
            outputDiv.textContent = '错误：请确保您已上传数据文件并输入了分析问题。';
            return;
        }

        outputDiv.innerHTML = ''; // 清空上次结果
        spinner.style.display = 'block';
        submitButton.disabled = true;

        try {
            const response = await fetch('/query', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || `服务器错误，状态码: ${response.status}`);
            }

            const result = await response.json();

            // 使用新的标准化响应格式处理结果
            if (result.status === 'success') {
                renderSuccessResult(result);
            } else {
                renderErrorResult(result);
            }

        } catch (error) {
            renderErrorResult({
                status: 'error',
                errorDetails: {
                    code: 'NetworkError',
                    message: error.message
                }
            });
        } finally {
            spinner.style.display = 'none';
            submitButton.disabled = false;
        }
    });

    // 渲染成功结果的函数
    function renderSuccessResult(result) {
        const { resultType, data } = result;

        switch (resultType) {
            case 'table':
                renderTable(data);
                break;
            case 'text':
                renderText(data.content);
                break;
            case 'plot':
                renderPlot(data.image, data.caption);
                break;
            case 'raw_json':
                renderJson(data);
                break;
            default:
                renderText(`未知的结果类型: ${resultType}`);
        }
    }

    // 渲染错误结果的函数
    function renderErrorResult(result) {
        const errorDetails = result.errorDetails || { code: 'Unknown', message: '未知错误' };
        outputDiv.innerHTML = `
            <div class="error-message">
                <h4>❌ 处理失败</h4>
                <p><strong>错误类型:</strong> ${errorDetails.code}</p>
                <p><strong>错误信息:</strong> ${errorDetails.message}</p>
            </div>
        `;
    }

    // 渲染表格数据
    function renderTable(data) {
        const { columns, data: rows, metadata } = data;

        let html = '<div class="success-message"><h4>✅ 查询成功 - 表格数据</h4></div>';

        // 添加元数据信息
        if (metadata) {
            html += `
                <div class="result-metadata">
                    <strong>数据信息:</strong>
                    共 ${metadata.total_rows} 行，${metadata.columns_count} 列
                    ${metadata.truncated ? `（显示前 ${metadata.displayed_rows} 行）` : ''}
                </div>
            `;
        }

        // 构建表格
        html += '<table class="result-table"><thead><tr>';
        columns.forEach(col => {
            html += `<th>${col}</th>`;
        });
        html += '</tr></thead><tbody>';

        rows.forEach(row => {
            html += '<tr>';
            row.forEach(cell => {
                html += `<td>${cell !== null ? cell : ''}</td>`;
            });
            html += '</tr>';
        });

        html += '</tbody></table>';
        outputDiv.innerHTML = html;
    }

    // 渲染文本结果
    function renderText(content) {
        outputDiv.innerHTML = `
            <div class="success-message">
                <h4>✅ 查询成功 - 文本结果</h4>
                <p>${content}</p>
            </div>
        `;
    }

    // 渲染图表
    function renderPlot(imageData, caption) {
        outputDiv.innerHTML = `
            <div class="success-message">
                <h4>✅ 查询成功 - 可视化图表</h4>
                <p>${caption}</p>
                <img src="${imageData}" alt="Generated Plot" style="max-width: 100%; border-radius: 4px; border: 1px solid #dcdfe6; margin-top: 10px;"/>
            </div>
        `;
    }

    // 渲染JSON数据
    function renderJson(data) {
        outputDiv.innerHTML = `
            <div class="success-message">
                <h4>✅ 查询成功 - JSON数据</h4>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>
            </div>
        `;
    }
</script>

</body>
</html>
