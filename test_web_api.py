#!/usr/bin/env python3
"""
测试 Web API 功能
"""

import requests
import os
import time

def test_web_api():
    """测试 Web API"""
    print("🔧 测试 Web API...")
    
    # 等待服务器启动
    time.sleep(2)
    
    # 测试服务器是否运行
    try:
        response = requests.get("http://127.0.0.1:5001", timeout=10)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 创建测试数据文件
    test_csv_content = """产品,销量,价格
手机,100,3000
电脑,50,8000
平板,75,2000"""
    
    with open("test_data.csv", "w", encoding="utf-8") as f:
        f.write(test_csv_content)
    
    # 测试有问题的查询
    test_queries = [
        "总销售额有多少，并分析构成",
        "计算每个产品的销售额占比",
        "显示销量最高的产品"
    ]
    
    for query in test_queries:
        print(f"\n🧠 测试查询: '{query}'")
        
        try:
            with open("test_data.csv", "rb") as f:
                files = {"file": ("test_data.csv", f, "text/csv")}
                data = {"query": query}
                
                response = requests.post(
                    "http://127.0.0.1:5001/query",
                    files=files,
                    data=data,
                    timeout=60
                )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求成功")
                print(f"📈 结果类型: {result.get('type', 'unknown')}")
                print(f"📈 结果内容: {result.get('value', 'No value')[:200]}...")
                
                # 检查是否包含语法错误
                if "unmatched" in str(result.get('value', '')):
                    print("❌ 仍然存在语法错误")
                else:
                    print("✅ 没有语法错误")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
    
    # 清理测试文件
    try:
        os.remove("test_data.csv")
    except:
        pass

if __name__ == "__main__":
    test_web_api()
