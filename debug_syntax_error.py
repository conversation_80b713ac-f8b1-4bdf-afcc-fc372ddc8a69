#!/usr/bin/env python3
"""
调试语法错误问题
"""

import os
import pandas as pd
import requests
import ast
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_problematic_query():
    """测试有问题的查询"""
    print("🔧 测试有问题的查询: '总销售额有多少，并分析构成'")
    
    api_key = os.environ.get("DASHSCOPE_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE")
    model = os.environ.get("LLM_MODEL", "qwen-plus")
    
    if not api_key or not api_base:
        print("❌ 配置不完整")
        return
    
    # 构建专门用于代码生成的提示
    query = "总销售额有多少，并分析构成"
    context = "DataFrame with columns: 产品, 销量, 价格"
    
    prompt = f"""
Based on the following data analysis question, generate Python pandas code to answer it.

Question: {query}

Data context: {context}

Requirements:
1. Use pandas operations (df is the dataframe variable)
2. The final answer must be stored in a variable called 'result'
3. Generate ONLY executable Python code, no markdown formatting
4. Do not include ```python or ``` tags
5. Make sure the last line assigns the final answer to 'result'
6. Ensure all brackets, parentheses, and braces are properly matched
7. Do not use any curly braces {{}} unless absolutely necessary

Example:
# Calculate total sales
total_sales = df['销量'] * df['价格']
result = total_sales.sum()

Please generate the appropriate pandas code (code only, no explanations):
"""
    
    url = f"{api_base}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": model,
        "messages": [
            {
                "role": "system",
                "content": "You are a Python data analysis expert. Generate ONLY clean, executable pandas code. No markdown formatting, no explanations, just pure Python code. Always ensure the final result is stored in a variable called 'result'. Avoid using curly braces {} unless absolutely necessary."
            },
            {
                "role": "user", 
                "content": prompt
            }
        ],
        "temperature": 0.1,
        "max_tokens": 4000
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        response.raise_for_status()
        
        result = response.json()
        if "choices" in result and len(result["choices"]) > 0:
            content = result["choices"][0]["message"]["content"].strip()
            
            print(f"🤖 原始响应:")
            print(f"'{content}'")
            print()
            
            # 清理响应，移除任何 markdown 格式
            if "```python" in content:
                # 提取代码块中的内容
                import re
                code_match = re.search(r'```python\s*(.*?)\s*```', content, re.DOTALL)
                if code_match:
                    content = code_match.group(1).strip()
            elif "```" in content:
                # 移除任何代码块标记
                import re
                content = re.sub(r'```.*?\n', '', content)
                content = re.sub(r'```', '', content)
                content = content.strip()
            
            print(f"🧹 清理后的代码:")
            print(f"'{content}'")
            print()
            
            # 检查语法
            try:
                ast.parse(content)
                print("✅ 语法检查通过")
            except SyntaxError as e:
                print(f"❌ 语法错误: {e}")
                print(f"错误位置: 行 {e.lineno}, 列 {e.offset}")
                if e.lineno:
                    lines = content.split('\n')
                    if e.lineno <= len(lines):
                        print(f"问题行: '{lines[e.lineno-1]}'")
                
                # 尝试修复常见的语法错误
                print("\n🔧 尝试修复语法错误...")
                fixed_content = fix_syntax_errors(content)
                print(f"修复后的代码:")
                print(f"'{fixed_content}'")
                
                try:
                    ast.parse(fixed_content)
                    print("✅ 修复后语法检查通过")
                    content = fixed_content
                except SyntaxError as e2:
                    print(f"❌ 修复失败: {e2}")
                    return
            
            # 测试代码执行
            test_data = pd.DataFrame({
                '产品': ['手机', '电脑', '平板'],
                '销量': [100, 50, 75],
                '价格': [3000, 8000, 2000]
            })
            
            try:
                df = test_data
                local_vars = {'df': df, 'pd': pd}
                exec(content, {'pd': pd}, local_vars)
                print(f"🎯 执行结果: {local_vars.get('result', 'No result')}")
            except Exception as e:
                print(f"❌ 代码执行错误: {e}")
            
        else:
            print("❌ API 返回格式错误")
            
    except Exception as e:
        print(f"❌ API 调用错误: {e}")

def fix_syntax_errors(code):
    """尝试修复常见的语法错误"""
    lines = code.split('\n')
    fixed_lines = []
    
    for line in lines:
        # 移除不匹配的大括号
        if '}' in line and '{' not in line:
            line = line.replace('}', '')
        
        # 修复不完整的赋值语句
        if line.strip().startswith('result =') and line.strip().endswith('}'):
            line = line.replace('}', '')
        
        # 移除空的大括号
        line = line.replace('{}', '')
        
        # 修复不匹配的括号
        open_parens = line.count('(')
        close_parens = line.count(')')
        if close_parens > open_parens:
            # 移除多余的右括号
            diff = close_parens - open_parens
            for _ in range(diff):
                line = line.replace(')', '', 1)
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

if __name__ == "__main__":
    test_problematic_query()
